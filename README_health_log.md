# Health Log Management System

A simple Python script that uses SQLite to manage health records with automatic record limit management.

## Features

- **SQLite Database**: Automatically creates and manages a `health_records.db` database
- **Health Records**: Stores weight, glucose, hemoglobin levels with automatic timestamps
- **Automatic Cleanup**: Maintains only the latest 5 records (configurable)
- **Input Validation**: Validates health data ranges for safety
- **Interactive Interface**: Command-line interface for easy interaction
- **Error Handling**: Comprehensive error handling and user feedback

## Database Schema

The `records` table contains the following fields:

| Field | Type | Description |
|-------|------|-------------|
| `id` | INTEGER | Auto-incremented primary key |
| `weight` | REAL | Person's weight in kg (20-500 kg) |
| `glucose` | REAL | Glucose level in mg/dL (50-600 mg/dL) |
| `hemoglobin` | REAL | Hemoglobin level in g/dL (5-25 g/dL) |
| `timestamp` | DATETIME | Automatically set creation time |

## Files

- `health_log.py` - Main health log management script
- `test_health_log.py` - Test script with sample data
- `README_health_log.md` - This documentation file

## Usage

### Running the Main Script

```bash
python health_log.py
```

This will start an interactive menu with the following options:

1. **Add new record** - Enter weight, glucose, and hemoglobin values
2. **Display all records** - Show all current records (latest first)
3. **Show record count** - Display the current number of records
4. **Delete all records** - Remove all records (with confirmation)
5. **Exit** - Close the application

### Running the Test Script

```bash
python test_health_log.py
```

This will:
- Create a test database with sample data
- Demonstrate the automatic cleanup feature
- Test input validation
- Show how the system maintains only the latest 5 records

### Using as a Module

```python
from health_log import HealthLogManager

# Initialize with default settings (5 records max)
health_log = HealthLogManager()

# Or customize the database name and record limit
health_log = HealthLogManager(db_name="my_health.db", max_records=10)

# Add a new record
success = health_log.insert_record(weight=70.5, glucose=95.0, hemoglobin=14.2)

# Display all records
records = health_log.display_records()

# Get record count
count = health_log.get_record_count()
```

## Input Validation

The system validates input values to ensure they are within reasonable ranges:

- **Weight**: 20-500 kg
- **Glucose**: 50-600 mg/dL
- **Hemoglobin**: 5-25 g/dL

Invalid inputs are rejected with appropriate error messages.

## Automatic Record Management

When a new record is inserted:

1. The record is added to the database with the current timestamp
2. The system checks if the total number of records exceeds the limit (default: 5)
3. If the limit is exceeded, the oldest records are automatically deleted
4. Only the most recent records are retained

## Example Output

```
Health Log Management System
========================================

Options:
1. Add new record
2. Display all records
3. Show record count
4. Delete all records
5. Exit

Enter your choice (1-5): 2

================================================================================
ID    Weight (kg)  Glucose (mg/dL)  Hemoglobin (g/dL)  Timestamp           
================================================================================
5     70.8         98.0             14.0               2024-01-15 10:30:45
4     71.5         105.0            14.3               2024-01-15 10:25:30
3     70.1         92.0             14.1               2024-01-15 10:20:15
2     69.8         125.0            13.9               2024-01-15 10:15:00
1     71.2         88.0             14.5               2024-01-15 10:10:45
================================================================================
Total records: 5
```

## Requirements

- Python 3.6 or higher
- SQLite3 (included with Python standard library)

## Error Handling

The system includes comprehensive error handling for:

- Database connection issues
- Invalid input values
- File system errors
- SQLite operation errors

All errors are logged with descriptive messages to help with troubleshooting.

## Integration with Existing Flask App

This health log system can be easily integrated with the existing Flask ML application by:

1. Importing the `HealthLogManager` class
2. Adding routes for health log management
3. Connecting patient sessions with their health records
4. Using the health data as additional features for ML predictions

Example integration:

```python
from health_log import HealthLogManager

# In your Flask app
health_log = HealthLogManager(db_name="patient_health_logs.db")

@app.route('/add_health_record', methods=['POST'])
def add_health_record():
    # Get patient data and add to health log
    # Use for ML feature enhancement
    pass
```
