#!/usr/bin/env python3
"""
Health Log Management System using SQLite

This script provides functionality to manage a simple health log database
with automatic record limit management (keeps only the latest 5 records).

Features:
- SQLite database connection and table creation
- Insert new health records with automatic timestamp
- Automatic cleanup to maintain only the latest 5 records
- Display all current records in descending order (latest first)
- Input validation and error handling
"""

import sqlite3
import datetime
from typing import Optional, List, Tuple
import os


class HealthLogManager:
    """
    A class to manage health records in an SQLite database.
    
    Attributes:
        db_name (str): Name of the SQLite database file
        max_records (int): Maximum number of records to keep (default: 5)
    """
    
    def __init__(self, db_name: str = "health_records.db", max_records: int = 5):
        """
        Initialize the HealthLogManager.
        
        Args:
            db_name (str): Name of the database file
            max_records (int): Maximum number of records to keep
        """
        self.db_name = db_name
        self.max_records = max_records
        self.create_database()
    
    def create_database(self) -> None:
        """
        Create the database and records table if they don't exist.
        """
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()
                
                # Create the records table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        weight REAL NOT NULL,
                        glucose REAL NOT NULL,
                        hemoglobin REAL NOT NULL,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                print(f"Database '{self.db_name}' initialized successfully.")
                
        except sqlite3.Error as e:
            print(f"Error creating database: {e}")
            raise
    
    def insert_record(self, weight: float, glucose: float, hemoglobin: float) -> bool:
        """
        Insert a new health record into the database.
        
        Args:
            weight (float): Person's weight
            glucose (float): Glucose level
            hemoglobin (float): Hemoglobin level
            
        Returns:
            bool: True if insertion was successful, False otherwise
        """
        # Validate input values
        if not self._validate_inputs(weight, glucose, hemoglobin):
            return False
        
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()
                
                # Insert the new record
                cursor.execute('''
                    INSERT INTO records (weight, glucose, hemoglobin)
                    VALUES (?, ?, ?)
                ''', (weight, glucose, hemoglobin))
                
                # Clean up old records to maintain the limit
                self._cleanup_old_records(cursor)
                
                conn.commit()
                print(f"Record inserted successfully: Weight={weight}, Glucose={glucose}, Hemoglobin={hemoglobin}")
                return True
                
        except sqlite3.Error as e:
            print(f"Error inserting record: {e}")
            return False
    
    def _validate_inputs(self, weight: float, glucose: float, hemoglobin: float) -> bool:
        """
        Validate input values for health records.
        
        Args:
            weight (float): Weight value to validate
            glucose (float): Glucose value to validate
            hemoglobin (float): Hemoglobin value to validate
            
        Returns:
            bool: True if all inputs are valid, False otherwise
        """
        try:
            # Convert to float and check for reasonable ranges
            weight = float(weight)
            glucose = float(glucose)
            hemoglobin = float(hemoglobin)
            
            # Basic validation ranges (adjust as needed)
            if not (20 <= weight <= 500):  # Weight in kg
                print("Error: Weight must be between 20 and 500 kg")
                return False
            
            if not (50 <= glucose <= 600):  # Glucose in mg/dL
                print("Error: Glucose level must be between 50 and 600 mg/dL")
                return False
            
            if not (5 <= hemoglobin <= 25):  # Hemoglobin in g/dL
                print("Error: Hemoglobin level must be between 5 and 25 g/dL")
                return False
            
            return True
            
        except (ValueError, TypeError):
            print("Error: All values must be valid numbers")
            return False
    
    def _cleanup_old_records(self, cursor: sqlite3.Cursor) -> None:
        """
        Remove old records to maintain the maximum record limit.
        
        Args:
            cursor (sqlite3.Cursor): Database cursor
        """
        try:
            # Count current records
            cursor.execute("SELECT COUNT(*) FROM records")
            record_count = cursor.fetchone()[0]
            
            # If we exceed the limit, delete the oldest records
            if record_count > self.max_records:
                records_to_delete = record_count - self.max_records
                
                cursor.execute('''
                    DELETE FROM records 
                    WHERE id IN (
                        SELECT id FROM records 
                        ORDER BY timestamp ASC, id ASC 
                        LIMIT ?
                    )
                ''', (records_to_delete,))
                
                print(f"Cleaned up {records_to_delete} old record(s)")
                
        except sqlite3.Error as e:
            print(f"Error during cleanup: {e}")
    
    def display_records(self) -> List[Tuple]:
        """
        Display all current records in descending order (latest first).
        
        Returns:
            List[Tuple]: List of record tuples
        """
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, weight, glucose, hemoglobin, timestamp
                    FROM records
                    ORDER BY timestamp DESC, id DESC
                ''')
                
                records = cursor.fetchall()
                
                if not records:
                    print("No records found in the database.")
                    return []
                
                # Display header
                print("\n" + "="*80)
                print(f"{'ID':<5} {'Weight (kg)':<12} {'Glucose (mg/dL)':<16} {'Hemoglobin (g/dL)':<18} {'Timestamp':<20}")
                print("="*80)
                
                # Display records
                for record in records:
                    record_id, weight, glucose, hemoglobin, timestamp = record
                    print(f"{record_id:<5} {weight:<12.1f} {glucose:<16.1f} {hemoglobin:<18.1f} {timestamp:<20}")
                
                print("="*80)
                print(f"Total records: {len(records)}")
                
                return records
                
        except sqlite3.Error as e:
            print(f"Error retrieving records: {e}")
            return []
    
    def get_record_count(self) -> int:
        """
        Get the current number of records in the database.
        
        Returns:
            int: Number of records
        """
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM records")
                return cursor.fetchone()[0]
        except sqlite3.Error as e:
            print(f"Error getting record count: {e}")
            return 0
    
    def delete_all_records(self) -> bool:
        """
        Delete all records from the database (for testing purposes).
        
        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM records")
                conn.commit()
                print("All records deleted successfully.")
                return True
        except sqlite3.Error as e:
            print(f"Error deleting records: {e}")
            return False


def main():
    """
    Main function to demonstrate the health log functionality.
    """
    print("Health Log Management System")
    print("="*40)
    
    # Initialize the health log manager
    health_log = HealthLogManager()
    
    while True:
        print("\nOptions:")
        print("1. Add new record")
        print("2. Display all records")
        print("3. Show record count")
        print("4. Delete all records")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            try:
                print("\nEnter health data:")
                weight = float(input("Weight (kg): "))
                glucose = float(input("Glucose level (mg/dL): "))
                hemoglobin = float(input("Hemoglobin level (g/dL): "))
                
                health_log.insert_record(weight, glucose, hemoglobin)
                
            except ValueError:
                print("Error: Please enter valid numeric values.")
        
        elif choice == '2':
            health_log.display_records()
        
        elif choice == '3':
            count = health_log.get_record_count()
            print(f"\nCurrent number of records: {count}")
        
        elif choice == '4':
            confirm = input("Are you sure you want to delete all records? (yes/no): ").lower()
            if confirm == 'yes':
                health_log.delete_all_records()
        
        elif choice == '5':
            print("Goodbye!")
            break
        
        else:
            print("Invalid choice. Please enter 1-5.")


if __name__ == "__main__":
    main()
