<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Diabetes Risk Prediction</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='styles.css') }}" rel="stylesheet">
</head>
<body>
    <div class="language-switch">
        <button class="language-btn" data-lang="en">EN</button>
        <button class="language-btn active" data-lang="ru">RU</button>
    </div>

    <div class="header header-bg">
        <div class="container">
            <div class="site-logo">
                <img src="{{ url_for('static', filename='images/health-logo.png') }}" alt="Health Logo">
            </div>
            <h1 data-en="Diabetes Risk" data-ru="Риск диабета">Diabetes Risk</h1>
            <p data-en="Please enter your name to continue" data-ru="Пожалуйста, введите ваше имя для продолжения">Please enter your name to continue</p>
            <div class="text-center mt-3">
                <div class="d-inline-block">
                    <span data-en="Студент:" data-ru="Студент:">Студент:</span>
                    <strong>Абоуелезз Хазем Тахер</strong>
                </div>
                <div class="d-inline-block ms-3">
                    <span data-en="Руководитель проекта:" data-ru="Руководитель проекта:">Руководитель проекта:</span>
                    <strong>Смирнов Андрей Алексеевич</strong>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-form">
                    <img src="{{ url_for('static', filename='images/user-icon.png') }}" alt="User Icon" class="medical-icon mx-auto d-block mb-4">
                    <form action="{{ url_for('login') }}" method="POST">
                        <div class="form-group">
                            <label for="patient_name" class="form-label" data-en="Patient Name" data-ru="Имя пациента">Patient Name</label>
                            <input type="text" class="form-control" id="patient_name" name="patient_name" required>
                        </div>
                        <button type="submit" class="btn-predict mt-4 w-100" data-en="Continue" data-ru="Продолжить">Continue</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentLang = 'ru';

        function updateLanguage(lang) {
            currentLang = lang;

            document.querySelectorAll('[data-' + lang + ']').forEach(element => {
                element.textContent = element.getAttribute('data-' + lang);
            });

            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.toggle('active', btn.getAttribute('data-lang') === lang);
            });
        }

        // Initial language update
        updateLanguage('ru');

        document.querySelectorAll('.language-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                updateLanguage(btn.getAttribute('data-lang'));
            });
        });
    </script>
</body>
</html>
