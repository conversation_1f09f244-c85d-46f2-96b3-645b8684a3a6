#!/usr/bin/env python3
"""
Test script for the Health Log Management System

This script demonstrates the functionality of the health_log.py module
by adding sample records and testing the automatic cleanup feature.
"""

import os
import sys
from health_log import HealthLogManager


def test_health_log():
    """
    Test the health log functionality with sample data.
    """
    print("Testing Health Log Management System")
    print("="*50)
    
    # Create a test database
    test_db = "test_health_records.db"
    
    # Remove existing test database if it exists
    if os.path.exists(test_db):
        os.remove(test_db)
        print(f"Removed existing test database: {test_db}")
    
    # Initialize health log manager with test database
    health_log = HealthLogManager(db_name=test_db, max_records=5)
    
    print(f"\nInitialized health log with max {health_log.max_records} records")
    
    # Test data - 7 records to test the cleanup functionality
    test_records = [
        (70.5, 95.0, 14.2),   # Normal values
        (72.0, 110.0, 13.8),  # Slightly elevated glucose
        (71.2, 88.0, 14.5),   # Normal values
        (69.8, 125.0, 13.9),  # Pre-diabetic glucose
        (70.1, 92.0, 14.1),   # Normal values
        (71.5, 105.0, 14.3),  # Slightly elevated glucose
        (70.8, 98.0, 14.0),   # Normal values
    ]
    
    print(f"\nAdding {len(test_records)} test records...")
    print("(This will test the automatic cleanup feature)")
    
    # Add all test records
    for i, (weight, glucose, hemoglobin) in enumerate(test_records, 1):
        print(f"\nAdding record {i}:")
        success = health_log.insert_record(weight, glucose, hemoglobin)
        if success:
            print(f"  ✓ Record {i} added successfully")
        else:
            print(f"  ✗ Failed to add record {i}")
        
        # Show current record count
        count = health_log.get_record_count()
        print(f"  Current record count: {count}")
    
    print(f"\n{'='*50}")
    print("Final Results:")
    print(f"{'='*50}")
    
    # Display all current records
    records = health_log.display_records()
    
    # Verify that only 5 records remain
    final_count = health_log.get_record_count()
    print(f"\nVerification:")
    print(f"Expected max records: {health_log.max_records}")
    print(f"Actual record count: {final_count}")
    
    if final_count == health_log.max_records:
        print("✓ Automatic cleanup working correctly!")
    else:
        print("✗ Automatic cleanup not working as expected!")
    
    # Test input validation
    print(f"\n{'='*50}")
    print("Testing Input Validation:")
    print(f"{'='*50}")
    
    invalid_test_cases = [
        (-10, 100, 14),      # Invalid weight
        (70, 1000, 14),      # Invalid glucose
        (70, 100, 50),       # Invalid hemoglobin
        ("abc", 100, 14),    # Non-numeric weight
    ]
    
    for i, (weight, glucose, hemoglobin) in enumerate(invalid_test_cases, 1):
        print(f"\nTest case {i}: Weight={weight}, Glucose={glucose}, Hemoglobin={hemoglobin}")
        success = health_log.insert_record(weight, glucose, hemoglobin)
        if not success:
            print("  ✓ Invalid input correctly rejected")
        else:
            print("  ✗ Invalid input was accepted (this shouldn't happen)")
    
    print(f"\n{'='*50}")
    print("Test completed!")
    print(f"Test database created: {test_db}")
    print("You can examine the database file or run the main script to interact with it.")
    print(f"{'='*50}")


def cleanup_test_database():
    """
    Clean up the test database file.
    """
    test_db = "test_health_records.db"
    if os.path.exists(test_db):
        try:
            os.remove(test_db)
            print(f"Test database {test_db} removed successfully.")
        except OSError as e:
            print(f"Error removing test database: {e}")


if __name__ == "__main__":
    try:
        test_health_log()
        
        # Ask user if they want to keep the test database
        print("\nDo you want to keep the test database for further examination?")
        keep_db = input("Enter 'yes' to keep, or any other key to delete: ").lower().strip()
        
        if keep_db != 'yes':
            cleanup_test_database()
        else:
            print("Test database kept. You can run 'python health_log.py' to interact with it.")
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
        cleanup_test_database()
    except Exception as e:
        print(f"\nUnexpected error during testing: {e}")
        cleanup_test_database()
        sys.exit(1)
